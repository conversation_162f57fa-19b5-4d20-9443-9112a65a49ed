import React, { Component } from 'react';
import { Result, Button } from 'antd';
import authService, { PERMISSIONS, ROLES } from '../../services/auth';

// 权限检查高阶组件
export const withPermission = (requiredPermissions = [], requiredRoles = []) => {
  return (WrappedComponent) => {
    return class PermissionWrapper extends Component {
      constructor(props) {
        super(props);
        this.state = {
          hasPermission: false,
          loading: true,
        };
      }

      componentDidMount() {
        this.checkPermission();
      }

      checkPermission = () => {
        const userInfo = authService.getUserInfo();

        if (!userInfo) {
          this.setState({ hasPermission: false, loading: false });
          return;
        }

        // 检查权限
        let hasRequiredPermissions = true;
        if (requiredPermissions.length > 0) {
          hasRequiredPermissions = requiredPermissions.every((permission) =>
            authService.hasPermission(permission));
        }

        // 检查角色
        let hasRequiredRoles = true;
        if (requiredRoles.length > 0) {
          hasRequiredRoles = requiredRoles.some((role) =>
            authService.hasRole(role));
        }

        const hasPermission = hasRequiredPermissions && hasRequiredRoles;
        this.setState({ hasPermission, loading: false });
      };

      render() {
        const { hasPermission, loading } = this.state;

        if (loading) {
          return <div>检查权限中...</div>;
        }

        if (!hasPermission) {
          return (
            <Result
              status="403"
              title="403"
              subTitle="抱歉，您没有权限访问此页面。"
              extra={
                <Button type="primary" onClick={() => window.history.back()}>
                  返回
                </Button>
              }
            />
          );
        }

        return <WrappedComponent {...this.props} />;
      }
    };
  };
};

// 权限检查组件
// eslint-disable-next-line react/no-multi-comp
export class PermissionGuard extends Component {
  checkPermission = () => {
    const { permissions = [], roles = [] } = this.props;

    if (!authService.isAuthenticated()) {
      return false;
    }

    // 检查权限
    let hasRequiredPermissions = true;
    if (permissions.length > 0) {
      hasRequiredPermissions = permissions.every((permission) =>
        authService.hasPermission(permission));
    }

    // 检查角色
    let hasRequiredRoles = true;
    if (roles.length > 0) {
      hasRequiredRoles = roles.some((role) =>
        authService.hasRole(role));
    }

    return hasRequiredPermissions && hasRequiredRoles;
  };

  render() {
    const { children, fallback = null } = this.props;

    if (this.checkPermission()) {
      return children;
    }

    return fallback;
  }
}

// 预定义的权限组件
export const ProjectPermissionGuard = ({ children, action, fallback = null }) => {
  const permissionMap = {
    view: [PERMISSIONS.PROJECT_VIEW],
    create: [PERMISSIONS.PROJECT_CREATE],
    edit: [PERMISSIONS.PROJECT_EDIT],
    delete: [PERMISSIONS.PROJECT_DELETE],
  };

  const permissions = permissionMap[action] || [];

  return (
    <PermissionGuard permissions={permissions} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

export const BrandPermissionGuard = ({ children, action, fallback = null }) => {
  const permissionMap = {
    view: [PERMISSIONS.BRAND_VIEW],
    create: [PERMISSIONS.BRAND_CREATE],
    edit: [PERMISSIONS.BRAND_EDIT],
    delete: [PERMISSIONS.BRAND_DELETE],
  };

  const permissions = permissionMap[action] || [];

  return (
    <PermissionGuard permissions={permissions} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

// 角色检查组件
export const RoleGuard = ({ children, roles = [], fallback = null }) => {
  return (
    <PermissionGuard roles={roles} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

// 管理员权限组件
export const AdminGuard = ({ children, fallback = null }) => {
  return (
    <RoleGuard roles={[ROLES.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
};

export default PermissionGuard;
