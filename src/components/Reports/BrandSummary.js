import React, { Component } from 'react';
import {
  Row,
  Col,
  Table,
  DatePicker,
  Spin,
  message,
  Icon,
  Tag,
} from 'antd';
import { brandSummaryAPI, dataTransformers } from '../../services/financialAPI';
import './BrandSummary.css';
// import {
//   <PERSON><PERSON><PERSON>,
//   Bar,
//   Pie<PERSON><PERSON>,
//   Pie,
//   Cell,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   Legend,
//   ResponsiveContainer,
// } from 'recharts';
import moment from 'moment';

// const { Option } = Select;
const { RangePicker } = DatePicker;

// 图表颜色配置
// const CHART_COLORS
// = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'];

class BrandSummary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      summaryData: {
        totalBrands: 0,
        totalProjects: 0,
        totalBudget: 0,
        totalCost: 0,
        totalProfit: 0,
        avgProfitMargin: 0,
        brandStats: [],
        projectStatusStats: [],
        monthlyTrend: [],
      },
      filters: {
        // 全年
        dateRange: [moment().startOf('year'), moment().endOf('year')],
      },
    };
  }

  componentDidMount() {
    this.loadSummaryData();
  }

  componentDidCatch(error, errorInfo) {
    console.error('BrandSummary component error:', error, errorInfo);
  }

  // 加载品牌汇总数据
  loadSummaryData = async () => {
    this.setState({ loading: true });

    try {
      const { filters } = this.state;
      const params = {};

      if (filters.dateRange && filters.dateRange.length === 2) {
        params.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        params.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }

      // 调用API获取品牌财务汇总报表
      try {
        const result = await brandSummaryAPI.getSummary(params);

        if (result.success) {
          // 使用数据转换器转换API数据格式
          const summaryData = dataTransformers.transformSummaryData(result.data);
          this.setState({ summaryData });
        } else {
          throw new Error(result.message || '获取数据失败');
        }
      } catch (apiError) {
        console.warn('API调用失败，使用模拟数据:', apiError);
      }
    } catch (error) {
      console.error('Load brand summary failed:', error);
      message.error('获取品牌汇总数据失败');
    } finally {
      this.setState({ loading: false });
    }
  };

  // 处理筛选条件变化
  handleFilterChange = (key, value) => {
    this.setState(
      (prevState) => ({
        filters: {
          ...prevState.filters,
          [key]: value,
        },
      }),
      () => {
        this.loadSummaryData();
      },
    );
  };

  // 格式化金额
  formatAmount = (amount) => {
    if (!amount) return '¥0';
    return `¥${amount.toLocaleString()}`;
  };

  // 渲染核心指标卡片
  renderOverallStats = () => {
    const { summaryData } = this.state;

    const metricsData = [
      {
        title: '总下单金额',
        value: summaryData.totalOrderAmount || 0,
        formatter: this.formatAmount,
        icon: 'shopping-cart',
        color: '#1890ff',
        trend: '+12.5%',
        category: 'primary',
      },
      {
        title: '已执行金额',
        value: summaryData.totalExecutedAmount || 0,
        formatter: this.formatAmount,
        icon: 'check-circle',
        color: '#52c41a',
        trend: '+8.3%',
        category: 'success',
      },
      {
        title: '预估毛利',
        value: summaryData.totalEstimatedProfit || 0,
        formatter: this.formatAmount,
        icon: 'rise',
        color: '#52c41a',
        trend: '+15.2%',
        category: 'success',
      },
      {
        title: '平均毛利率',
        value: summaryData.avgProfitMargin || 0,
        suffix: '%',
        precision: 1,
        icon: 'percentage',
        color: '#722ed1',
        trend: '+2.1%',
        category: 'info',
      },
      {
        title: '已回款',
        value: summaryData.totalReceivedAmount || 0,
        formatter: this.formatAmount,
        icon: 'bank',
        color: '#52c41a',
        trend: '+18.7%',
        category: 'success',
      },
      {
        title: '未回款',
        value: summaryData.totalUnreceivedAmount || 0,
        formatter: this.formatAmount,
        icon: 'clock-circle',
        color: '#f5222d',
        trend: '-5.4%',
        category: 'warning',
      },
      {
        title: '回款率',
        value: summaryData.totalOrderAmount > 0 ?
          ((summaryData.totalReceivedAmount / summaryData.totalOrderAmount) * 100) : 0,
        suffix: '%',
        precision: 1,
        icon: 'fund',
        color: '#1890ff',
        trend: '+3.2%',
        category: 'primary',
      },
      {
        title: '品牌总数',
        value: summaryData.totalBrands || 0,
        icon: 'tags',
        color: '#1890ff',
        trend: '+2',
        category: 'info',
      },
    ];

    return (
      <Row gutter={[16, 16]}>
        {metricsData.map((metric) => (
          <Col xs={24} sm={12} lg={6} key={metric.title}>
            <div className={`metric-card metric-${metric.category}`}>
              <div className="metric-header">
                <div className="metric-icon">
                  <Icon type={metric.icon} />
                </div>
                <div className="metric-content">
                  <div className="metric-title">{metric.title}</div>
                  <div className="metric-value" style={{ color: metric.color }}>
                    {metric.formatter ?
                      metric.formatter(metric.value) :
                      `${metric.value.toLocaleString()}${metric.suffix || ''}`
                    }
                  </div>
                </div>
              </div>
              <div className="metric-trend">
                <span className={metric.trend.startsWith('+') ? 'trend-up' : 'trend-down'}>
                  {metric.trend}
                </span>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    );
  };

  // 渲染品牌排行榜
  renderBrandRanking = () => {
    const { summaryData } = this.state;

    const columns = [
      {
        title: '排名',
        dataIndex: 'rank',
        key: 'rank',
        width: 50,
        fixed: 'left',
        render: (rank) => (
          <Tag color={rank <= 3 ? 'gold' : 'default'} size="small">
            {rank}
          </Tag>
        ),
      },
      {
        title: '品牌名称',
        dataIndex: 'brandName',
        key: 'brandName',
        width: 100,
        fixed: 'left',
        ellipsis: true,
      },
      {
        title: '项目数',
        dataIndex: 'projectCount',
        key: 'projectCount',
        width: 60,
        sorter: (a, b) => a.projectCount - b.projectCount,
      },
      {
        title: '下单金额',
        dataIndex: 'orderAmount',
        key: 'orderAmount',
        width: 100,
        render: (amount) => this.formatAmount(amount),
        sorter: (a, b) => (a.orderAmount || 0) - (b.orderAmount || 0),
      },
      {
        title: '已执行',
        dataIndex: 'executedAmount',
        key: 'executedAmount',
        width: 100,
        render: (amount) => this.formatAmount(amount),
        sorter: (a, b) => (a.executedAmount || 0) - (b.executedAmount || 0),
      },
      {
        title: '执行中',
        dataIndex: 'executingAmount',
        key: 'executingAmount',
        width: 100,
        render: (amount) => this.formatAmount(amount),
        sorter: (a, b) => (a.executingAmount || 0) - (b.executingAmount || 0),
      },
      {
        title: '预估毛利',
        dataIndex: 'estimatedProfit',
        key: 'estimatedProfit',
        width: 100,
        render: (amount) => (
          <span style={{ color: amount >= 0 ? '#137333' : '#d93025' }}>
            {this.formatAmount(amount)}
          </span>
        ),
        sorter: (a, b) => (a.estimatedProfit || 0) - (b.estimatedProfit || 0),
      },
      {
        title: '毛利率',
        dataIndex: 'profitMargin',
        key: 'profitMargin',
        width: 70,
        render: (margin) => {
          let color = '#d93025';
          if (margin >= 20) {
            color = '#137333';
          } else if (margin >= 10) {
            color = '#f29900';
          }
          return (
            <span style={{ color }}>
              {margin ? margin.toFixed(1) : 0}%
            </span>
          );
        },
        sorter: (a, b) => (a.profitMargin || 0) - (b.profitMargin || 0),
      },
      {
        title: '已回款',
        dataIndex: 'receivedAmount',
        key: 'receivedAmount',
        width: 100,
        render: (amount) => (
          <span style={{ color: '#137333' }}>
            {this.formatAmount(amount)}
          </span>
        ),
        sorter: (a, b) => (a.receivedAmount || 0) - (b.receivedAmount || 0),
      },
      {
        title: '未回款',
        dataIndex: 'unreceivedAmount',
        key: 'unreceivedAmount',
        width: 100,
        render: (amount) => (
          <span style={{ color: '#d93025' }}>
            {this.formatAmount(amount)}
          </span>
        ),
        sorter: (a, b) => (a.unreceivedAmount || 0) - (b.unreceivedAmount || 0),
      },
      {
        title: '已支付',
        dataIndex: 'paidAmount',
        key: 'paidAmount',
        width: 100,
        render: (amount) => (
          <span style={{ color: '#137333' }}>
            {this.formatAmount(amount)}
          </span>
        ),
        sorter: (a, b) => (a.paidAmount || 0) - (b.paidAmount || 0),
      },
      {
        title: '未支付',
        dataIndex: 'unpaidAmount',
        key: 'unpaidAmount',
        width: 100,
        render: (amount) => (
          <span style={{ color: '#d93025' }}>
            {this.formatAmount(amount)}
          </span>
        ),
        sorter: (a, b) => (a.unpaidAmount || 0) - (b.unpaidAmount || 0),
      },
      {
        title: '备注',
        dataIndex: 'remarks',
        key: 'remarks',
        width: 150,
        ellipsis: true,
        render: (text) => (
          <span title={text}>{text || '-'}</span>
        ),
      },
    ];

    return (
      <div className="modern-table-container">
        <div className="table-header">
          <div className="table-title">
            <Icon type="table" className="table-icon" />
            品牌财务详细数据
          </div>
          <div className="table-actions">
            <span className="data-count">
              共 {summaryData.brandStats ? summaryData.brandStats.length : 0} 个品牌
            </span>
          </div>
        </div>
        <div className="table-content">
          <Table
            columns={columns}
            dataSource={summaryData.brandStats}
            rowKey="brandId"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`,
            }}
            size="small"
            scroll={{ x: 1200 }}
            className="modern-table"
          />
        </div>
      </div>
    );
  };

  // 渲染品牌预算分布图
  renderBudgetDistribution = () => {
    const { summaryData } = this.state;

    return (
      <div className="chart-card">
        <div className="chart-header">
          <div className="chart-title">
            <Icon type="pie-chart" className="chart-icon" />
            品牌预算分布
          </div>
        </div>
        <div className="chart-content">
          <div className="chart-placeholder">
            <Icon type="pie-chart" className="placeholder-icon" />
            <div className="placeholder-text">
              <h4>图表开发中</h4>
              <p>品牌数据: {summaryData.brandStats ? summaryData.brandStats.length : 0} 个品牌</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染项目状态统计
  renderProjectStatusStats = () => {
    const { summaryData } = this.state;

    return (
      <div className="chart-card">
        <div className="chart-header">
          <div className="chart-title">
            <Icon type="bar-chart" className="chart-icon" />
            项目状态分布
          </div>
        </div>
        <div className="chart-content">
          <div className="chart-placeholder">
            <Icon type="bar-chart" className="placeholder-icon" />
            <div className="placeholder-text">
              <h4>图表开发中</h4>
              <p>
                状态数据: {summaryData.projectStatusStats ?
                summaryData.projectStatusStats.length : 0} 种状态
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  render() {
    const { loading, filters } = this.state;

    try {
      return (
        <div className="modern-brand-summary">
          <Spin spinning={loading}>
            {/* 页面头部 */}
            <div className="summary-header">
              <div className="header-content">
                <div className="header-left">
                  <h2 className="page-title">
                    <Icon type="fund" className="title-icon" />
                    品牌财务汇总
                  </h2>
                  <p className="page-description">
                    全面掌握各品牌财务状况和执行进度
                  </p>
                </div>
                <div className="header-filters">
                  <div className="filter-item">
                    <span className="filter-label">时间范围</span>
                    <RangePicker
                      value={filters.dateRange}
                      onChange={(dates) => this.handleFilterChange('dateRange', dates)}
                      format="YYYY-MM-DD"
                      className="modern-date-picker"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 核心指标卡片 */}
            {/* <div className="metrics-section">
              {this.renderOverallStats()}
            </div> */}

            {/* 品牌详细数据表格 */}
            <div className="table-section">
              {this.renderBrandRanking()}
            </div>

            {/* 数据可视化区域 */}
            {/* <div className="charts-section">
              <Row gutter={24}>
                <Col xs={24} lg={12}>
                  {this.renderBudgetDistribution()}
                </Col>
                <Col xs={24} lg={12}>
                  {this.renderProjectStatusStats()}
                </Col>
              </Row>
            </div> */}
          </Spin>
        </div>
      );
    } catch (error) {
      console.error('BrandSummary render error:', error);
      return (
        <div className="modern-brand-summary error-state">
          <div className="error-content">
            <Icon type="exclamation-circle" className="error-icon" />
            <h3>数据加载失败</h3>
            <p>请刷新页面重试或联系管理员</p>
            <p className="error-detail">错误详情: {error.message}</p>
          </div>
        </div>
      );
    }
  }
}

export default BrandSummary;
