/**
 * 财务报表相关API服务
 */
// API服务层
// import dayjs from 'dayjs';
import config from '../config.js';
import authService from './auth.js';

const { host } = config;
const API_BASE_URL = `${host}/api`;
const BASE_URL = '/financial';
// 通用请求方法
const request = async (url, options = {}) => {
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 添加认证头
  const accessToken = authService.getAccessToken();
  if (accessToken) {
    defaultOptions.headers.Authorization = `Bearer ${accessToken}`;
  }

  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, finalOptions);

    // 处理401未授权错误
    if (response.status === 401) {
      // Token可能已过期，尝试刷新
      try {
        const refreshed = await authService.refreshAccessToken();
        if (refreshed) {
          // 重新添加新的token
          const newAccessToken = authService.getAccessToken();
          if (newAccessToken) {
            finalOptions.headers.Authorization = `Bearer ${newAccessToken}`;
            // 重新发起请求
            const retryResponse = await fetch(`${API_BASE_URL}${url}`, finalOptions);
            const retryData = await retryResponse.json();

            if (!retryResponse.ok) {
              throw new Error(retryData.message || `HTTP error! status: ${retryResponse.status}`);
            }

            return retryData;
          }
        }
        throw new Error('Token刷新失败');
      } catch (refreshError) {
        // Token刷新失败，跳转到登录页
        console.error('Token refresh failed:', refreshError);
        authService.clearAuth();
        // 可以在这里触发登录页面跳转
        window.location.reload();
        throw new Error('登录已过期，请重新登录');
      }
    }

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

/**
 * 品牌财务汇总报表API
 */
export const brandSummaryAPI = {
  /**
   * 获取品牌财务汇总报表
   * @param {Object} params - 查询参数
   * @param {string} params.brandId - 品牌ID过滤
   * @param {string} params.startDate - 开始日期 (YYYY-MM-DD)
   * @param {string} params.endDate - 结束日期 (YYYY-MM-DD)
   * @param {Array} params.projectStatus - 项目状态过滤
   * @param {string} params.includeCompleted - 是否包含已完成项目
   * @param {string} params.includeCancelled - 是否包含已取消项目
   * @returns {Promise} API响应
   */
  getSummary: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `${BASE_URL}/brands/summary${queryString ? `?${queryString}` : ''}`;
    return await request(url);
  },

  /**
   * 获取品牌财务详细报表
   * @param {string} brandId - 品牌ID
   * @param {Object} params - 查询参数
   * @param {string} params.startDate - 开始日期 (YYYY-MM-DD)
   * @param {string} params.endDate - 结束日期 (YYYY-MM-DD)
   * @param {Array} params.projectStatus - 项目状态过滤
   * @param {string} params.includeCompleted - 是否包含已完成项目
   * @param {string} params.includeCancelled - 是否包含已取消项目
   * @returns {Promise} API响应
   */
  getBrandDetail: async (brandId, params = {}) => {
    // 处理项目状态数组参数
    const queryParams = new URLSearchParams();

    Object.keys(params).forEach((key) => {
      if (key === 'projectStatus' && Array.isArray(params[key])) {
        // 项目状态数组需要特殊处理
        params[key].forEach((status) => {
          queryParams.append('projectStatus', status);
        });
      } else if (params[key] !== undefined && params[key] !== null) {
        queryParams.append(key, params[key]);
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_URL}/brands/${brandId}/detail${queryString ? `?${queryString}` : ''}`;
    return await request(url);
  },
};

/**
 * 数据转换工具
 */
export const dataTransformers = {
  /**
   * 转换品牌汇总数据格式
   * @param {Object} apiData - API返回的数据
   * @returns {Object} 组件需要的数据格式
   */
  transformSummaryData: (apiData) => {
    const { summary, brands } = apiData;

    return {
      totalBrands: summary.totalBrands,
      totalProjects: brands.reduce((sum, brand) => sum + brand.projectCount, 0),
      totalOrderAmount: summary.totalOrderAmount,
      totalExecutedAmount: summary.totalExecutedAmount,
      totalExecutingAmount: brands.reduce((sum, brand) => sum + brand.executingAmount, 0),
      totalEstimatedProfit: summary.totalEstimatedProfit,
      avgProfitMargin: summary.overallProfitMargin,
      totalReceivedAmount: summary.totalReceivedAmount,
      totalUnreceivedAmount: summary.totalOrderAmount - summary.totalReceivedAmount,
      totalPaidAmount: summary.totalPaidAmount,
      totalUnpaidAmount: brands.reduce((sum, brand) => sum + brand.unpaidProjectAmount, 0),
      brandStats: brands.map((brand, index) => ({
        rank: index + 1,
        brandId: brand.brandId,
        brandName: brand.brandName,
        projectCount: brand.projectCount,
        orderAmount: brand.orderAmount,
        executedAmount: brand.executedAmount,
        executingAmount: brand.executingAmount,
        estimatedProfit: brand.estimatedProfit,
        profitMargin: brand.estimatedProfitMargin,
        receivedAmount: brand.receivedAmount,
        unreceivedAmount: brand.unreceivedAmount, // 注意API中的拼写
        paidAmount: brand.paidProjectAmount,
        unpaidAmount: brand.unpaidProjectAmount,
        remarks: brand.remarks || '-',
      })),
      projectStatusStats: [],
      monthlyTrend: [],
    };
  },

  /**
   * 转换品牌详细数据格式
   * @param {Object} apiData - API返回的数据
   * @returns {Object} 组件需要的数据格式
   */
  transformBrandDetailData: (apiData) => {
    const { brandInfo, summary, projects, generatedAt, reportPeriod } = apiData;

    return {
      brandInfo: {
        id: brandInfo.id,
        name: brandInfo.name,
        description: brandInfo.description,
        logo: brandInfo.logo,
      },
      projectStats: {
        totalProjects: summary.projectCount,
        totalBudget: summary.orderAmount,
        totalCost: summary.orderAmount - summary.estimatedProfit,
        totalProfit: summary.estimatedProfit,
        avgProfitMargin: summary.estimatedProfitMargin,
      },
      projects: projects.map((project) => ({
        id: project.id,
        projectName: project.projectName,
        executionPeriod: [
          project.period.startDate.split('T')[0],
          project.period.endDate.split('T')[0],
        ],
        status: project.status,
        planningBudget: project.budget.planningBudget,
        projectProfit: project.profit.profit,
        grossMargin: project.profit.grossMargin,
        executionProgress: dataTransformers.calculateExecutionProgress(project),
        documentType: project.documentType,
        contractType: project.contractType,
        executorPM: project.executorPMInfo?.name || project.executorPM,
      })),
      monthlyTrend: [],
      reportMeta: {
        generatedAt,
        reportPeriod,
      },
    };
  },

  /**
   * 计算项目执行进度
   * @param {Object} project - 项目数据
   * @returns {number} 执行进度百分比
   */
  calculateExecutionProgress: (project) => {
    // 根据项目状态和时间计算进度
    if (project.status === 'completed') return 100;
    if (project.status === 'cancelled') return 0;

    const startDate = new Date(project.period.startDate);
    const endDate = new Date(project.period.endDate);
    const now = new Date();

    if (now < startDate) return 0;
    if (now > endDate) return 100;

    const totalDuration = endDate.getTime() - startDate.getTime();
    const elapsed = now.getTime() - startDate.getTime();

    return Math.round((elapsed / totalDuration) * 100);
  },
};

/**
 * 模拟数据生成器（用于开发和测试）
 */
export const mockDataGenerators = {
  /**
   * 生成品牌汇总模拟数据
   */
  generateSummaryMockData: () => ({
    totalBrands: 3,
    totalProjects: 15,
    totalOrderAmount: 4500000,
    totalExecutedAmount: 2900000,
    totalExecutingAmount: 1600000,
    totalEstimatedProfit: 1215000,
    avgProfitMargin: 27.0,
    totalReceivedAmount: 2300000,
    totalUnreceivedAmount: 2200000,
    totalPaidAmount: 2000000,
    totalUnpaidAmount: 2500000,
    brandStats: [
      {
        rank: 1,
        brandId: 'brand-001',
        brandName: '品牌A',
        projectCount: 6,
        orderAmount: 1800000,
        executedAmount: 1200000,
        executingAmount: 600000,
        estimatedProfit: 540000,
        profitMargin: 30.0,
        receivedAmount: 1000000,
        unreceivedAmount: 800000,
        paidAmount: 900000,
        unpaidAmount: 900000,
        remarks: '重点客户，执行顺利',
      },
      {
        rank: 2,
        brandId: 'brand-002',
        brandName: '品牌B',
        projectCount: 4,
        orderAmount: 1200000,
        executedAmount: 800000,
        executingAmount: 400000,
        estimatedProfit: 300000,
        profitMargin: 25.0,
        receivedAmount: 600000,
        unreceivedAmount: 600000,
        paidAmount: 500000,
        unpaidAmount: 700000,
        remarks: '新客户，需要重点关注',
      },
      {
        rank: 3,
        brandId: 'brand-003',
        brandName: '品牌C',
        projectCount: 5,
        orderAmount: 1500000,
        executedAmount: 900000,
        executingAmount: 600000,
        estimatedProfit: 375000,
        profitMargin: 25.0,
        receivedAmount: 700000,
        unreceivedAmount: 800000,
        paidAmount: 600000,
        unpaidAmount: 900000,
        remarks: '长期合作伙伴',
      },
    ],
    projectStatusStats: [],
    monthlyTrend: [],
  }),

  /**
   * 生成品牌详细模拟数据
   */
  generateBrandDetailMockData: (brandId, brandName) => ({
    brandInfo: {
      id: brandId,
      name: brandName,
      description: '模拟数据',
    },
    projectStats: {
      totalProjects: 6,
      totalBudget: 1800000,
      totalCost: 1200000,
      totalProfit: 600000,
      avgProfitMargin: 33.3,
    },
    projects: [
      {
        id: 'project-001',
        projectName: '春节营销活动',
        executionPeriod: ['2024-02-01', '2024-02-29'],
        status: 'active',
        planningBudget: 300000,
        projectProfit: 90000,
        grossMargin: 30.0,
        executionProgress: 75,
        documentType: 'PROJECT_EXECUTION',
        contractType: 'ANNUAL_FRAME',
        executorPM: '张三',
      },
      {
        id: 'project-002',
        projectName: '618购物节推广',
        executionPeriod: ['2024-06-01', '2024-06-18'],
        status: 'completed',
        planningBudget: 500000,
        projectProfit: 150000,
        grossMargin: 30.0,
        executionProgress: 100,
        documentType: 'PROJECT_EXECUTION',
        contractType: 'SINGLE_PROJECT',
        executorPM: '李四',
      },
    ],
    monthlyTrend: [
      { month: '2024-01', plannedAmount: 200000, receivedAmount: 140000 },
      { month: '2024-02', plannedAmount: 300000, receivedAmount: 210000 },
      { month: '2024-03', plannedAmount: 250000, receivedAmount: 175000 },
    ],
    revenueAnalysis: {
      totalPlannedRevenue: 1800000,
      totalReceivedRevenue: 1000000,
    },
    costAnalysis: {
      totalWeeklyBudgets: 15,
      totalPaidAmount: 900000,
      totalUnpaidAmount: 900000,
    },
  }),
};

export const financialExportAPI = {
  exportReport: async (params) => {
    try {
      const response = await request(`${BASE_URL}/export/download?${new URLSearchParams(params).toString()}`, {
        method: 'get',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || '导出报表失败');
      }
    } catch (error) {
      console.error('Export report failed:', error);
      throw error;
    }
  },
};

export default {
  brandSummaryAPI,
  dataTransformers,
  mockDataGenerators,
  financialExportAPI,
};

