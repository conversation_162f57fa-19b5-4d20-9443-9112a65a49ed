// 周预算管理相关工具函数
import moment from 'moment';

/**
 * 格式化金额显示
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额字符串
 */
export const formatCurrency = (amount) => {
  if (!amount && amount !== 0) return '-';
  return `¥${amount.toLocaleString()}`;
};

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '-';
  return moment(date).format(format);
};

/**
 * 格式化日期范围
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {string} 格式化后的日期范围字符串
 */
export const formatDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return '-';
  return `${formatDate(startDate)} 至 ${formatDate(endDate)}`;
};

// 服务类型选项
export const SERVICE_TYPES = [
  { value: 'influencer', label: '达人服务' },
  { value: 'advertising', label: '投流服务' },
  { value: 'other', label: '其他服务' },
];

// 税率选项
export const TAX_RATES = [
  { value: 'special_1', label: '专票1%' },
  { value: 'special_3', label: '专票3%' },
  { value: 'special_6', label: '专票6%' },
  { value: 'general', label: '普票' },
];

// 周预算状态选项
export const WEEKLY_BUDGET_STATUS = [
  { value: 'created', label: '已创建', color: '#d9d9d9' },
  { value: 'approved', label: '已审批', color: '#1890ff' },
  { value: 'executing', label: '执行中', color: '#fa8c16' },
  { value: 'completed', label: '已完成', color: '#52c41a' },
  { value: 'cancelled', label: '已取消', color: '#8c8c8c' },
];

/**
 * 获取服务类型标签
 * @param {string} type - 服务类型
 * @returns {string} 服务类型标签
 */
export const getServiceTypeLabel = (type) => {
  const serviceType = SERVICE_TYPES.find((item) => item.value === type);
  return serviceType ? serviceType.label : type;
};

/**
 * 获取税率标签
 * @param {string} rate - 税率
 * @returns {string} 税率标签
 */
export const getTaxRateLabel = (rate) => {
  const taxRate = TAX_RATES.find((item) => item.value === rate);
  return taxRate ? taxRate.label : rate;
};

/**
 * 获取周预算状态配置
 * @param {string} status - 周预算状态
 * @returns {object} 状态配置对象
 */
export const getWeeklyBudgetStatusConfig = (status) => {
  return WEEKLY_BUDGET_STATUS.find((item) => item.value === status) || {
    label: status,
    color: 'default',
  };
};

/**
 * 计算剩余金额
 * @param {number} contractAmount - 合同金额
 * @param {number} paidAmount - 已付金额
 * @returns {number} 剩余金额
 */
export const calculateRemainingAmount = (contractAmount, paidAmount) => {
  return (contractAmount || 0) - (paidAmount || 0);
};

/**
 * 计算支付进度百分比
 * @param {number} contractAmount - 合同金额
 * @param {number} paidAmount - 已付金额
 * @returns {number} 支付进度百分比
 */
export const calculatePaymentProgress = (contractAmount, paidAmount) => {
  if (!contractAmount || contractAmount === 0) return 0;
  return Math.round(((paidAmount || 0) / contractAmount) * 100);
};

/**
 * 获取周数和年份
 * @param {string|Date} date - 日期
 * @returns {object} 包含年份和周数的对象
 */
export const getWeekInfo = (date) => {
  const momentDate = moment(date);
  return {
    year: momentDate.year(),
    weekNumber: momentDate.week(),
  };
};

/**
 * 获取月份中的第几周 - 严格 ISO 8601 算法
 * @param {string|Date} date - 日期
 * @returns {{ year: number, month: number, weekOfMonth: number }}
 */
export const getMonthWeekInfo2 = (date) => {
  const m = moment(date);
  const year = m.isoWeekYear(); // ISO 周所属的年份
  const month = m.month() + 1; // 1-12

  const firstOfMonth = m.clone().startOf('month');
  const firstWeek = firstOfMonth.isoWeek(); // 第几周
  const curWeek = m.isoWeek(); // 当前周
  let weekOfMonth = curWeek - firstWeek + 1;

  // 跨年（通常发生在 12 月底 / 1 月初）
  if (curWeek < firstWeek) {
    const totalWeeks = firstOfMonth.isoWeeksInYear();
    weekOfMonth = curWeek + totalWeeks - firstWeek + 1;
  }

  return { year, month, weekOfMonth, monthName: m.format('M月'), displayText: `${year}年${month}月第${weekOfMonth}周` };
};

/**
 * 获取“完整周”起算，并且：
 * - 如果本周周一在上个月，则算作本月第 1 周
 * - 如果本周周日跨到下个月，则算作本月最后一周 + 1
 *
 * @param {string|Date} date
 * @returns {{
*   year: number,
*   month: number,
*   weekOfMonth: number,   // 1…n
*   monthName: string,
*   displayText: string
* }}
*/
export const getMonthWeekInfo = (date) => {
  const m = moment(date);
  const year = m.year();
  const month = m.month() + 1;
  const monthName = m.format('M月');
  const day = m.date();

  // 1. 计算本月第一个完整周的周一
  const firstOfMonth = m.clone().startOf('month');
  const firstDow = firstOfMonth.day(); // 0=日…6=六
  const firstMonDate = firstDow === 1
    ? 1
    : 1 + ((8 - firstDow) % 7);

  // 2. 本周的周一和周日
  const weekStart = m.clone().startOf('isoWeek'); // 周一
  const weekEnd = m.clone().endOf('isoWeek'); // 周日

  let weekOfMonth;

  // 如果本周的周一在上个月，把它当作本月的第1周
  if (weekStart.month() + 1 < month) {
    weekOfMonth = 1;

    // 如果本周的周日在下个月，把它当作本月的“最后一周+1”
  } else if (weekEnd.month() + 1 > month) {
    // 计算本月一共有多少个完整周
    // 从 firstMonDate 每 7 天一周，直到 <= 当月天数
    const daysInMonth = m.clone().endOf('month').date();
    const fullWeeksCount = Math.floor((daysInMonth - firstMonDate + 1) / 7);
    weekOfMonth = fullWeeksCount + 1;

    // 否则，正常计算是第几个完整周
  } else {
    weekOfMonth = Math.floor((day - firstMonDate) / 7) + 1;
  }

  // 组装展示
  const displayText = `${year}年${month}月第${weekOfMonth}周`;

  return { year, month, weekOfMonth, monthName, displayText };
};

/**
 * 获取月份中的第几周 - 按加法计算（7月第一周=7.1-7.7，第二周=7.8-7.14）
 * @param {string|Date} date - 日期
 * @returns {{
 *   year: number,
 *   month: number,
 *   weekOfMonth: number,   // 1…n
 *   monthName: string,
 *   displayText: string,
 *   weekStartDate: string, // 该周的开始日期 YYYY-MM-DD
 *   weekEndDate: string    // 该周的结束日期 YYYY-MM-DD
 * }}
 */
export const getMonthWeekInfoByAddition = (date) => {
  const m = moment(date);
  const year = m.year();
  const month = m.month() + 1;
  const monthName = m.format('M月');
  const day = m.date();

  // 按加法计算：第一周是1-7号，第二周是8-14号，以此类推
  const weekOfMonth = Math.ceil(day / 7);

  // 计算该周的开始和结束日期
  const weekStartDay = (weekOfMonth - 1) * 7 + 1;
  const weekEndDay = Math.min(weekOfMonth * 7, m.clone().endOf('month').date());

  const weekStartDate = m.clone().date(weekStartDay).format('YYYY-MM-DD');
  const weekEndDate = m.clone().date(weekEndDay).format('YYYY-MM-DD');

  // 组装展示
  const displayText = `${year}年${month}月第${weekOfMonth}周`;

  return {
    year,
    month,
    weekOfMonth,
    monthName,
    displayText,
    weekStartDate,
    weekEndDate,
  };
};

/**
 * 格式化周期显示文本
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {string} 格式化的周期显示文本
 */
export const formatWeekPeriodDisplay = (startDate, endDate) => {
  if (!startDate || !endDate) return '-';

  const start = moment(startDate);
  const end = moment(endDate);
  const monthWeekInfo = getMonthWeekInfoByAddition(startDate);

  return `${monthWeekInfo.displayText} (${start.format('MM-DD')} 至 ${end.format('MM-DD')})`;
};

/**
 * 生成指定月份的所有周期选项（按加法计算）
 * @param {number} year - 年份
 * @param {number} month - 月份 (1-12)
 * @param {moment} projectStartDate - 项目开始日期（可选）
 * @param {moment} projectEndDate - 项目结束日期（可选）
 * @returns {Array} 周期选项数组
 */
export const generateMonthWeekOptions = (
  year,
  month,
  projectStartDate = null,
  projectEndDate = null,
) => {
  const options = [];
  const monthMoment = moment({ year, month: month - 1 }); // moment的月份是0-11
  const daysInMonth = monthMoment.daysInMonth();

  // 计算有多少个完整的7天周期
  const totalWeeks = Math.ceil(daysInMonth / 7);

  for (let weekNum = 1; weekNum <= totalWeeks; weekNum++) {
    const weekStartDay = (weekNum - 1) * 7 + 1;
    const weekEndDay = Math.min(weekNum * 7, daysInMonth);

    const weekStartDate = monthMoment.clone().date(weekStartDay);
    const weekEndDate = monthMoment.clone().date(weekEndDay);

    // 检查是否在项目周期范围内
    let disabled = false;
    if (projectStartDate && weekEndDate.isBefore(projectStartDate, 'day')) {
      disabled = true;
    }
    if (projectEndDate && weekStartDate.isAfter(projectEndDate, 'day')) {
      disabled = true;
    }

    options.push({
      value: `${year}-${month.toString().padStart(2, '0')}-W${weekNum}`,
      label: `${month}月第${weekNum}周 (${weekStartDay}-${weekEndDay}日)`,
      displayText: `${month}月第${weekNum}周 (${weekStartDay}-${weekEndDay}日)`,
      weekStartDate: weekStartDate.format('YYYY-MM-DD'),
      weekEndDate: weekEndDate.format('YYYY-MM-DD'),
      weekStartDay,
      weekEndDay,
      weekNum,
      year,
      month,
      disabled,
    });
  }

  return options;
};

/**
 * 获取一周的开始和结束日期
 * @param {string|Date} date - 日期
 * @returns {object} 包含开始和结束日期的对象
 */
export const getWeekRange = (date) => {
  const momentDate = moment(date);
  const startOfWeek = momentDate.clone().startOf('week');
  const endOfWeek = momentDate.clone().endOf('week');

  return {
    startDate: startOfWeek.format('YYYY-MM-DD'),
    endDate: endOfWeek.format('YYYY-MM-DD'),
  };
};

/**
 * 生成周预算标题
 * @param {number} weekNumber - 周数
 * @param {string} serviceType - 服务类型
 * @returns {string} 周预算标题
 */
export const generateWeeklyBudgetTitle = (weekNumber, serviceType) => {
  const serviceLabel = getServiceTypeLabel(serviceType);
  return `第${weekNumber}周${serviceLabel}预算`;
};

/**
 * 验证周预算表单数据
 * @param {object} formData - 表单数据
 * @returns {object} 验证结果
 */
export const validateWeeklyBudgetForm = (formData) => {
  const errors = {};

  if (!formData.title || formData.title.trim() === '') {
    errors.title = '预算标题不能为空';
  }

  if (!formData.weekStartDate) {
    errors.weekStartDate = '请选择周开始日期';
  }

  if (!formData.weekEndDate) {
    errors.weekEndDate = '请选择周结束日期';
  }

  if (formData.weekStartDate && formData.weekEndDate) {
    if (moment(formData.weekStartDate).isAfter(moment(formData.weekEndDate))) {
      errors.weekEndDate = '结束日期不能早于开始日期';
    }
  }

  if (!formData.serviceType) {
    errors.serviceType = '请选择服务类型';
  }

  if (!formData.contractAmount || formData.contractAmount <= 0) {
    errors.contractAmount = '合同金额必须大于0';
  }

  if (formData.paidAmount && formData.paidAmount < 0) {
    errors.paidAmount = '已付金额不能为负数';
  }

  if (formData.paidAmount && formData.contractAmount &&
      formData.paidAmount > formData.contractAmount) {
    errors.paidAmount = '已付金额不能超过合同金额';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * 格式化周预算数据用于显示
 * @param {object} weeklyBudget - 周预算数据
 * @returns {object} 格式化后的周预算数据
 */
export const formatWeeklyBudgetForDisplay = (weeklyBudget) => {
  const remainingAmount = calculateRemainingAmount(
    weeklyBudget.contractAmount,
    weeklyBudget.paidAmount,
  );
  const paymentProgress = calculatePaymentProgress(
    weeklyBudget.contractAmount,
    weeklyBudget.paidAmount,
  );

  return {
    ...weeklyBudget,
    serviceTypeLabel: getServiceTypeLabel(weeklyBudget.serviceType),
    taxRateLabel: getTaxRateLabel(weeklyBudget.taxRate),
    statusConfig: getWeeklyBudgetStatusConfig(weeklyBudget.status),
    contractAmountDisplay: formatCurrency(weeklyBudget.contractAmount),
    paidAmountDisplay: formatCurrency(weeklyBudget.paidAmount),
    remainingAmount,
    remainingAmountDisplay: formatCurrency(remainingAmount),
    paymentProgress,
    weekRangeDisplay: formatDateRange(weeklyBudget.weekStartDate, weeklyBudget.weekEndDate),
    createdAtDisplay: formatDate(weeklyBudget.createdAt),
    updatedAtDisplay: formatDate(weeklyBudget.updatedAt),
  };
};

/**
 * 生成项目执行周期内的周列表
 * @param {string} startDate - 项目开始日期
 * @param {string} endDate - 项目结束日期
 * @returns {array} 周列表
 */
export const generateProjectWeeks = (startDate, endDate) => {
  const weeks = [];
  const start = moment(startDate).startOf('week');
  const end = moment(endDate).endOf('week');

  const current = start.clone();
  let weekNumber = 1;

  while (current.isSameOrBefore(end)) {
    const weekStart = current.clone();
    const weekEnd = current.clone().endOf('week');

    // 确保不超出项目范围
    const actualStart = moment.max(weekStart, moment(startDate));
    const actualEnd = moment.min(weekEnd, moment(endDate));

    weeks.push({
      weekNumber,
      weekStartDate: actualStart.format('YYYY-MM-DD'),
      weekEndDate: actualEnd.format('YYYY-MM-DD'),
      year: actualStart.year(),
      week: actualStart.week(),
    });

    current.add(1, 'week');
    weekNumber++;
  }

  return weeks;
};

/**
 * 审批状态配置
 */
export const APPROVAL_STATUS = [
  { value: 'PENDING', label: '审批中', color: 'processing' },
  { value: 'APPROVED', label: '已通过', color: 'success' },
  { value: 'REJECTED', label: '已拒绝', color: 'error' },
  { value: 'CANCELLED', label: '已取消', color: 'default' },
];

/**
 * 获取审批状态配置
 * @param {string} status - 审批状态
 * @returns {object} 状态配置对象
 */
export const getApprovalStatusConfig = (status) => {
  const config = APPROVAL_STATUS.find((item) => item.value === status);
  return config || { value: status, label: status, color: 'default' };
};

/**
 * 合同签署主体选项
 */
export const CONTRACT_ENTITIES = [
  { value: '看尚互娱科技（广州）有限公司', label: '看尚互娱科技（广州）有限公司' },
  { value: '深圳中科华海科技有限公司', label: '深圳中科华海科技有限公司' },
  { value: '华海恒裕（深圳）贸易有限公司', label: '华海恒裕（深圳）贸易有限公司' },
];

/**
 * 获取合同签署主体标签
 * @param {string} entity - 合同签署主体
 * @returns {string} 标签
 */
export const getContractEntityLabel = (entity) => {
  const config = CONTRACT_ENTITIES.find((item) => item.value === entity);
  return config ? config.label : entity;
};

/**
 * 付款方式选项
 */
export const PAYMENT_METHODS = [
  { value: 'bank_transfer', label: '银行转账' },
  { value: 'online_payment', label: '在线支付' },
  { value: 'check', label: '支票' },
  { value: 'cash', label: '现金' },
  { value: 'other', label: '其他' },
];

/**
 * 获取付款方式标签
 * @param {string} method - 付款方式
 * @returns {string} 标签
 */
export const getPaymentMethodLabel = (method) => {
  const config = PAYMENT_METHODS.find((item) => item.value === method);
  return config ? config.label : method;
};

/**
 * 验证审批表单数据
 * @param {object} formData - 表单数据
 * @returns {object} 验证结果
 */
export const validateApprovalForm = (formData) => {
  const errors = {};

  if (!formData.totalAmount || formData.totalAmount <= 0) {
    errors.totalAmount = '付款金额必须大于0';
  }

  if (!formData.paymentReason || formData.paymentReason.trim() === '') {
    errors.paymentReason = '付款事由不能为空';
  }

  if (!formData.expectedPaymentDate) {
    errors.expectedPaymentDate = '期望付款时间不能为空';
  }

  if (!formData.receivingAccount) {
    errors.receivingAccount = '收款账号信息不能为空';
  } else {
    if (!formData.receivingAccount.accountName) {
      errors.accountName = '账户名称不能为空';
    }
    if (!formData.receivingAccount.accountNumber) {
      errors.accountNumber = '账号不能为空';
    }
    if (!formData.receivingAccount.bankName) {
      errors.bankName = '开户银行不能为空';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * 格式化审批数据用于显示
 * @param {object} approval - 审批数据
 * @returns {object} 格式化后的审批数据
 */
export const formatApprovalForDisplay = (approval) => {
  return {
    ...approval,
    statusConfig: getApprovalStatusConfig(approval.status),
    contractEntityLabel: getContractEntityLabel(approval.contractEntity),
    paymentMethodLabel: getPaymentMethodLabel(approval.paymentMethod),
    approvalAmountDisplay: formatCurrency(approval.approvalAmount),
    createTimeDisplay: formatDate(approval.createTime, 'YYYY-MM-DD HH:mm'),
    finishTimeDisplay: approval.finishTime ? formatDate(approval.finishTime, 'YYYY-MM-DD HH:mm') : '-',
    expectedPaymentDateDisplay: formatDate(approval.expectedPaymentDate),
  };
};

/**
 * 过滤周预算列表
 * @param {array} weeklyBudgets - 周预算列表
 * @param {object} filters - 过滤条件
 * @returns {array} 过滤后的周预算列表
 */
export const filterWeeklyBudgets = (weeklyBudgets, filters) => {
  return weeklyBudgets.filter((budget) => {
    // 服务类型过滤
    if (filters.serviceType && budget.serviceType !== filters.serviceType) {
      return false;
    }

    // 状态过滤
    if (filters.status && budget.status !== filters.status) {
      return false;
    }

    // 供应商过滤
    if (filters.supplierId && budget.supplierId !== filters.supplierId) {
      return false;
    }

    // 年份过滤
    if (filters.year && budget.year !== filters.year) {
      return false;
    }

    return true;
  });
};

/**
 * 排序周预算列表
 * @param {array} weeklyBudgets - 周预算列表
 * @param {string} sortBy - 排序字段
 * @param {string} sortOrder - 排序方向
 * @returns {array} 排序后的周预算列表
 */
export const sortWeeklyBudgets = (weeklyBudgets, sortBy, sortOrder = 'asc') => {
  return [...weeklyBudgets].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    // 处理日期字段
    if (sortBy === 'weekStartDate' || sortBy === 'weekEndDate' ||
        sortBy === 'createdAt' || sortBy === 'updatedAt') {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }

    // 处理数字字段
    if (sortBy === 'contractAmount' || sortBy === 'paidAmount' ||
        sortBy === 'weekNumber' || sortBy === 'year') {
      aValue = Number(aValue) || 0;
      bValue = Number(bValue) || 0;
    }

    // 处理字符串字段
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) {
      return sortOrder === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortOrder === 'asc' ? 1 : -1;
    }
    return 0;
  });
};
